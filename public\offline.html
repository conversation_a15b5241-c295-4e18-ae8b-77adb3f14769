<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CNEWS Admin - Offline</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .offline-container {
            text-align: center;
            max-width: 500px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .offline-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 24px;
            background: #60a5fa;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
        }
        
        h1 {
            font-size: 28px;
            margin-bottom: 16px;
            color: #60a5fa;
        }
        
        p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 24px;
            color: #d1d5db;
        }
        
        .retry-button {
            background: #60a5fa;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .retry-button:hover {
            background: #3b82f6;
        }
        
        .features {
            margin-top: 32px;
            text-align: left;
        }
        
        .features h3 {
            font-size: 18px;
            margin-bottom: 16px;
            color: #60a5fa;
        }
        
        .features ul {
            list-style: none;
        }
        
        .features li {
            padding: 8px 0;
            color: #d1d5db;
            position: relative;
            padding-left: 24px;
        }
        
        .features li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #10b981;
            font-weight: bold;
        }
        
        @media (max-width: 480px) {
            .offline-container {
                padding: 24px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            .offline-icon {
                width: 60px;
                height: 60px;
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">
            📱
        </div>
        
        <h1>CNEWS Admin</h1>
        <p>Você está offline, mas o aplicativo ainda funciona! Algumas funcionalidades estão disponíveis mesmo sem conexão com a internet.</p>
        
        <button class="retry-button" onclick="window.location.reload()">
            Tentar Novamente
        </button>
        
        <div class="features">
            <h3>Funcionalidades Offline:</h3>
            <ul>
                <li>Visualizar notícias em cache</li>
                <li>Navegar pela interface</li>
                <li>Marcar notícias como lidas</li>
                <li>Filtrar notícias localmente</li>
                <li>Sincronização automática quando voltar online</li>
            </ul>
        </div>
    </div>
    
    <script>
        // Check for online status
        function updateOnlineStatus() {
            if (navigator.onLine) {
                // Redirect to main app when back online
                window.location.href = '/';
            }
        }
        
        // Listen for online/offline events
        window.addEventListener('online', updateOnlineStatus);
        window.addEventListener('offline', updateOnlineStatus);
        
        // Check every 5 seconds if we're back online
        setInterval(updateOnlineStatus, 5000);
        
        // Service worker registration check
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then(function(registration) {
                console.log('Service Worker is ready');
            });
        }
    </script>
</body>
</html>
