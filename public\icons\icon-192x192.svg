<svg width="192" height="192" viewBox="0 0 192 192" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="192" height="192" rx="24" fill="url(#bgGradient)"/>
  
  <!-- News icon -->
  <rect x="48" y="48" width="96" height="67.19999999999999" rx="3.84" fill="#ffffff"/>
  <rect x="57.599999999999994" y="61.44" width="76.80000000000001" height="3.84" rx="1.92" fill="#1f2937"/>
  <rect x="57.599999999999994" y="72.96000000000001" width="57.599999999999994" height="3.84" rx="1.92" fill="#1f2937"/>
  <rect x="57.599999999999994" y="84.48" width="67.19999999999999" height="3.84" rx="1.92" fill="#1f2937"/>
  <rect x="57.599999999999994" y="96" width="48" height="3.84" rx="1.92" fill="#1f2937"/>
  
  <!-- CNEWS Text -->
  <text x="96" y="144" font-family="Arial, sans-serif" font-size="15.36" font-weight="bold" text-anchor="middle" fill="#60a5fa">CNEWS</text>
  <text x="96" y="163.2" font-family="Arial, sans-serif" font-size="7.68" text-anchor="middle" fill="#9ca3af">ADMIN</text>
</svg>