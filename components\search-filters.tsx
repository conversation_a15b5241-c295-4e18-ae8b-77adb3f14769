"use client";

import { useState, useMemo, useCallback } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Search,
  Filter,
  CalendarIcon,
  X,
  SlidersHorizontal,
} from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { cn } from "@/lib/utils";
import {
  FILTER_VALUES,
  FILTER_LABELS,
  MAJOR_NEWS_SOURCES,
} from "@/lib/constants";
import { useDebouncedCallback } from "@/hooks/useDebounce";

interface SearchFiltersProps {
  onSearchAction: (query: string) => void;
  onFilterByAuthorAction: (author: string) => void;
  onFilterByDateAction: (date: Date | undefined) => void;
  onFilterByReadStatusAction: (showUnreadOnly: boolean) => void;
  onSortChangeAction: (sort: string) => void;
  onClearFiltersAction: () => void;
  authors: string[];
  activeFilters: {
    search: string;
    author: string;
    date: Date | undefined;
    showUnreadOnly: boolean;
    sort: string;
  };
}

export function SearchFilters({
  onSearchAction,
  onFilterByAuthorAction,
  onFilterByDateAction,
  onFilterByReadStatusAction,
  onSortChangeAction,
  onClearFiltersAction,
  authors,
  activeFilters,
}: SearchFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [localSearchValue, setLocalSearchValue] = useState(
    activeFilters.search
  );

  // Debounced search to improve performance
  const debouncedSearch = useDebouncedCallback((value: string) => {
    onSearchAction(value);
  }, 300);

  // Get the current author filter value, handling empty string as "all"
  const currentAuthorFilter =
    activeFilters.author === ""
      ? FILTER_VALUES.ALL
      : activeFilters.author || FILTER_VALUES.PRINCIPAIS;

  // Get the display label for the current filter
  const getAuthorDisplayLabel = useMemo(() => {
    if (!currentAuthorFilter || currentAuthorFilter === FILTER_VALUES.ALL) {
      return FILTER_LABELS.all;
    }
    if (currentAuthorFilter === FILTER_VALUES.PRINCIPAIS) {
      return FILTER_LABELS.principais;
    }
    return currentAuthorFilter; // Individual author name
  }, [currentAuthorFilter]);

  const hasActiveFilters =
    activeFilters.search ||
    (activeFilters.author &&
      activeFilters.author !== FILTER_VALUES.PRINCIPAIS &&
      activeFilters.author !== "") ||
    activeFilters.date ||
    activeFilters.showUnreadOnly;

  const handleAuthorChange = useCallback(
    (value: string) => {
      onFilterByAuthorAction(value === FILTER_VALUES.ALL ? "" : value);
    },
    [onFilterByAuthorAction]
  );

  const handleSearchChange = useCallback(
    (value: string) => {
      setLocalSearchValue(value);
      debouncedSearch(value);
    },
    [debouncedSearch]
  );

  // Sync local search value with external changes
  useMemo(() => {
    if (activeFilters.search !== localSearchValue) {
      setLocalSearchValue(activeFilters.search);
    }
  }, [activeFilters.search, localSearchValue]);

  return (
    <div className="space-y-4 bg-white p-3 sm:p-4 rounded-2xl border shadow-xs">
      {/* Busca Principal */}
      <div className="flex flex-col sm:flex-row gap-2 sm:gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Buscar notícias por título, conteúdo ou autor..."
            value={localSearchValue}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10 h-11 text-sm sm:text-base"
          />
        </div>
        <Button
          variant="outline"
          onClick={() => setShowAdvanced(!showAdvanced)}
          className={cn(
            "h-11 px-3 sm:px-4 transition-all duration-200 flex items-center justify-center gap-2 min-w-0",
            showAdvanced
              ? "bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
              : "hover:bg-gray-50"
          )}
          title={
            showAdvanced
              ? "Ocultar filtros avançados"
              : "Mostrar filtros avançados"
          }
        >
          <SlidersHorizontal
            className={cn(
              "h-4 w-4 transition-transform duration-200 flex-shrink-0",
              showAdvanced && "rotate-180"
            )}
          />
          <span className="text-xs sm:text-sm hidden sm:inline">
            {showAdvanced ? "Ocultar" : "Filtros"}
          </span>
        </Button>
      </div>

      {/* Filtros Avançados */}
      {showAdvanced && (
        <div className="space-y-4 pt-4 border-t">
          {/* Mobile: Stack vertically, Desktop: Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Filtro por Autor */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                <span>Autor</span>
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full w-fit">
                  {currentAuthorFilter === FILTER_VALUES.PRINCIPAIS
                    ? "Principais fontes"
                    : currentAuthorFilter === FILTER_VALUES.ALL
                    ? "Todas as fontes"
                    : "Fonte específica"}
                </span>
              </label>
              <Select
                value={currentAuthorFilter}
                onValueChange={handleAuthorChange}
              >
                <SelectTrigger className="h-11 bg-white border-gray-200 hover:border-gray-300 focus:border-blue-500 transition-colors text-sm">
                  <SelectValue>
                    <span className="flex items-center gap-2 truncate">
                      {currentAuthorFilter === FILTER_VALUES.PRINCIPAIS && (
                        <span className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></span>
                      )}
                      {currentAuthorFilter === FILTER_VALUES.ALL && (
                        <span className="w-2 h-2 bg-gray-500 rounded-full flex-shrink-0"></span>
                      )}
                      {currentAuthorFilter !== FILTER_VALUES.PRINCIPAIS &&
                        currentAuthorFilter !== FILTER_VALUES.ALL && (
                          <span className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0"></span>
                        )}
                      <span className="truncate">{getAuthorDisplayLabel}</span>
                    </span>
                  </SelectValue>
                </SelectTrigger>
                <SelectContent className="max-h-80 w-full">
                  <SelectItem
                    value={FILTER_VALUES.PRINCIPAIS}
                    className="font-medium"
                  >
                    <div className="flex items-center gap-2 w-full">
                      <span className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></span>
                      <span className="truncate">
                        {FILTER_LABELS.principais}
                      </span>
                      <span className="text-xs text-gray-500 ml-auto flex-shrink-0">
                        ({MAJOR_NEWS_SOURCES.length} fontes)
                      </span>
                    </div>
                  </SelectItem>
                  <SelectItem value={FILTER_VALUES.ALL} className="font-medium">
                    <div className="flex items-center gap-2 w-full">
                      <span className="w-2 h-2 bg-gray-500 rounded-full flex-shrink-0"></span>
                      <span className="truncate">{FILTER_LABELS.all}</span>
                      <span className="text-xs text-gray-500 ml-auto flex-shrink-0">
                        ({authors.length} fontes)
                      </span>
                    </div>
                  </SelectItem>
                  {authors.length > 0 && (
                    <>
                      <div className="px-2 py-1.5 text-xs font-medium text-gray-500 border-t">
                        Fontes individuais
                      </div>
                      {authors.map((author) => (
                        <SelectItem
                          key={author}
                          value={author}
                          className="pl-6"
                        >
                          <div className="flex items-center gap-2 w-full">
                            <span className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0"></span>
                            <span className="truncate">{author}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </>
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Filtro por Data */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Data</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal h-11 text-sm",
                      !activeFilters.date && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4 flex-shrink-0" />
                    <span className="truncate">
                      {activeFilters.date
                        ? format(activeFilters.date, "PPP", { locale: ptBR })
                        : "Selecionar data"}
                    </span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={activeFilters.date}
                    onSelect={onFilterByDateAction}
                    autoFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Filtro por Status de Leitura */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Status de Leitura
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="unread-filter"
                  checked={activeFilters.showUnreadOnly}
                  onChange={(e) => onFilterByReadStatusAction(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label
                  htmlFor="unread-filter"
                  className="text-sm text-gray-700 cursor-pointer"
                >
                  Mostrar apenas não lidas
                </label>
              </div>
            </div>

            {/* Ordenação */}
            <div className="space-y-2 sm:col-span-2 lg:col-span-1">
              <label className="text-sm font-medium text-gray-700">
                Ordenar por
              </label>
              <Select
                value={activeFilters.sort}
                onValueChange={onSortChangeAction}
              >
                <SelectTrigger className="h-11 text-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date-desc">Mais recentes</SelectItem>
                  <SelectItem value="date-asc">Mais antigas</SelectItem>
                  <SelectItem value="author">Autor (A-Z)</SelectItem>
                  <SelectItem value="title">Título (A-Z)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      )}

      {/* Filtros Ativos */}
      {hasActiveFilters && (
        <div className="bg-blue-50 border border-blue-200  rounded-xl p-4">
          <div className="space-y-3">
            {/* Header */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-blue-700 flex items-center gap-1">
                <Filter className="h-3 w-3 flex-shrink-0" />
                Filtros ativos:
              </span>
              <Button
                variant="outline"
                size="lg"
                onClick={onClearFiltersAction}
                className="text-red-600 hover:text-red-700 hover:bg-red-50 transition-colors text-xs h-7 px-2"
              >
                <X className="h-3 w-3 mr-1 flex-shrink-0" />
                <span className="hidden sm:inline">Limpar todos</span>
                <span className="sm:hidden">Limpar</span>
              </Button>
            </div>

            {/* Badges */}
            <div className="flex flex-wrap gap-2">
              {activeFilters.search && (
                <Badge
                  variant="secondary"
                  className="flex items-center gap-1 bg-white border-blue-200 text-xs max-w-full p-3"
                >
                  <Search className="h-3 w-3 flex-shrink-0" />
                  <span className="truncate max-w-[120px] sm:max-w-[200px]">
                    Busca: {activeFilters.search}
                  </span>
                  <X
                    className="h-3 w-3 cursor-pointer hover:text-red-600 transition-colors flex-shrink-0"
                    onClick={() => onSearchAction("")}
                  />
                </Badge>
              )}
              {currentAuthorFilter !== FILTER_VALUES.PRINCIPAIS && (
                <Badge
                  variant="secondary"
                  className="flex items-center gap-1 bg-white border-blue-200 text-xs max-w-full p-3"
                >
                  <span
                    className={cn(
                      "w-2 h-2 rounded-full flex-shrink-0",
                      currentAuthorFilter === FILTER_VALUES.ALL
                        ? "bg-gray-500"
                        : "bg-green-500"
                    )}
                  ></span>
                  <span className="truncate max-w-[120px] sm:max-w-[200px]">
                    Autor:{" "}
                    {currentAuthorFilter === FILTER_VALUES.ALL
                      ? FILTER_LABELS.all
                      : currentAuthorFilter}
                  </span>
                  <X
                    className="h-3 w-3 cursor-pointer hover:text-red-600 transition-colors flex-shrink-0"
                    onClick={() =>
                      onFilterByAuthorAction(FILTER_VALUES.PRINCIPAIS)
                    }
                  />
                </Badge>
              )}
              {activeFilters.date && (
                <Badge
                  variant="secondary"
                  className="flex items-center gap-1 bg-white border-blue-200 text-xs p-3"
                >
                  <CalendarIcon className="h-3 w-3 flex-shrink-0" />
                  <span>Data: {format(activeFilters.date, "dd/MM/yyyy")}</span>
                  <X
                    className="h-3 w-3 cursor-pointer hover:text-red-600 transition-colors flex-shrink-0"
                    onClick={() => onFilterByDateAction(undefined)}
                  />
                </Badge>
              )}
              {activeFilters.showUnreadOnly && (
                <Badge
                  variant="secondary"
                  className="flex items-center gap-1 bg-white border-blue-200 text-xs p-3"
                >
                  <span className="w-2 h-2 bg-orange-500 rounded-full flex-shrink-0"></span>
                  <span>Apenas não lidas</span>
                  <X
                    className="h-3 w-3 cursor-pointer hover:text-red-600 transition-colors flex-shrink-0"
                    onClick={() => onFilterByReadStatusAction(false)}
                  />
                </Badge>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
