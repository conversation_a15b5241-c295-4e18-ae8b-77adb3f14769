<svg width="152" height="152" viewBox="0 0 152 152" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="152" height="152" rx="19" fill="url(#bgGradient)"/>
  
  <!-- News icon -->
  <rect x="38" y="38" width="76" height="53.199999999999996" rx="3.04" fill="#ffffff"/>
  <rect x="45.6" y="48.64" width="60.800000000000004" height="3.04" rx="1.52" fill="#1f2937"/>
  <rect x="45.6" y="57.76" width="45.6" height="3.04" rx="1.52" fill="#1f2937"/>
  <rect x="45.6" y="66.88" width="53.199999999999996" height="3.04" rx="1.52" fill="#1f2937"/>
  <rect x="45.6" y="76" width="38" height="3.04" rx="1.52" fill="#1f2937"/>
  
  <!-- CNEWS Text -->
  <text x="76" y="114" font-family="Arial, sans-serif" font-size="12.16" font-weight="bold" text-anchor="middle" fill="#60a5fa">CNEWS</text>
  <text x="76" y="129.2" font-family="Arial, sans-serif" font-size="6.08" text-anchor="middle" fill="#9ca3af">ADMIN</text>
</svg>