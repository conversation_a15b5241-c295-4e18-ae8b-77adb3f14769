<svg width="96" height="96" viewBox="0 0 96 96" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="96" height="96" rx="12" fill="url(#bgGradient)"/>
  
  <!-- News icon -->
  <rect x="24" y="24" width="48" height="33.599999999999994" rx="1.92" fill="#ffffff"/>
  <rect x="28.799999999999997" y="30.72" width="38.400000000000006" height="1.92" rx="0.96" fill="#1f2937"/>
  <rect x="28.799999999999997" y="36.480000000000004" width="28.799999999999997" height="1.92" rx="0.96" fill="#1f2937"/>
  <rect x="28.799999999999997" y="42.24" width="33.599999999999994" height="1.92" rx="0.96" fill="#1f2937"/>
  <rect x="28.799999999999997" y="48" width="24" height="1.92" rx="0.96" fill="#1f2937"/>
  
  <!-- CNEWS Text -->
  <text x="48" y="72" font-family="Arial, sans-serif" font-size="8" font-weight="bold" text-anchor="middle" fill="#60a5fa">CNEWS</text>
  <text x="48" y="81.6" font-family="Arial, sans-serif" font-size="6" text-anchor="middle" fill="#9ca3af">ADMIN</text>
</svg>