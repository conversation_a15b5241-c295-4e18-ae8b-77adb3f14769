<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="128" height="128" rx="16" fill="url(#bgGradient)"/>
  
  <!-- News icon -->
  <rect x="32" y="32" width="64" height="44.8" rx="2.56" fill="#ffffff"/>
  <rect x="38.4" y="40.96" width="51.2" height="2.56" rx="1.28" fill="#1f2937"/>
  <rect x="38.4" y="48.64" width="38.4" height="2.56" rx="1.28" fill="#1f2937"/>
  <rect x="38.4" y="56.32" width="44.8" height="2.56" rx="1.28" fill="#1f2937"/>
  <rect x="38.4" y="64" width="32" height="2.56" rx="1.28" fill="#1f2937"/>
  
  <!-- CNEWS Text -->
  <text x="64" y="96" font-family="Arial, sans-serif" font-size="10.24" font-weight="bold" text-anchor="middle" fill="#60a5fa">CNEWS</text>
  <text x="64" y="108.8" font-family="Arial, sans-serif" font-size="6" text-anchor="middle" fill="#9ca3af">ADMIN</text>
</svg>