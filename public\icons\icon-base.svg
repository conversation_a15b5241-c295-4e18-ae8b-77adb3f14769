<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="512" height="512" rx="64" fill="url(#bgGradient)"/>
  
  <!-- News icon background -->
  <circle cx="256" cy="200" r="80" fill="#ffffff" opacity="0.1"/>
  
  <!-- News/Document icon -->
  <rect x="200" y="160" width="112" height="80" rx="8" fill="#ffffff"/>
  <rect x="216" y="176" width="80" height="4" rx="2" fill="#1f2937"/>
  <rect x="216" y="188" width="64" height="4" rx="2" fill="#1f2937"/>
  <rect x="216" y="200" width="72" height="4" rx="2" fill="#1f2937"/>
  <rect x="216" y="212" width="56" height="4" rx="2" fill="#1f2937"/>
  
  <!-- CNEWS Text -->
  <text x="256" y="320" font-family="Arial, sans-serif" font-size="48" font-weight="bold" text-anchor="middle" fill="url(#textGradient)">CNEWS</text>
  <text x="256" y="360" font-family="Arial, sans-serif" font-size="24" text-anchor="middle" fill="#9ca3af">ADMIN</text>
  
  <!-- Sync arrows -->
  <g transform="translate(256, 400)">
    <circle r="30" fill="none" stroke="#60a5fa" stroke-width="3" opacity="0.6"/>
    <path d="M -15 -8 L -8 -15 L -8 -10 L 8 -10 L 8 -6 L -8 -6 L -8 -1 Z" fill="#60a5fa"/>
    <path d="M 15 8 L 8 15 L 8 10 L -8 10 L -8 6 L 8 6 L 8 1 Z" fill="#60a5fa"/>
  </g>
</svg>
