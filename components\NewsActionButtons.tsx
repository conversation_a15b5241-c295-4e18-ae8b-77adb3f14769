/**
 * NewsActionButtons - Action buttons component with refresh, sync and reset functionality
 * Contains the three main action buttons extracted from NewsHeader
 */

"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { RefreshCw, Loader2, Trash2, Play } from "lucide-react";
import { useSync } from "@/hooks/useSync";

/**
 * Props interface for NewsActionButtons component
 */
interface NewsActionButtonsProps {
  totalNews: number;
  onRefreshAction: () => void;
  onResetAction: () => Promise<void>;
  isResetting: boolean;
  variant?: "mobile" | "desktop";
}

/**
 * Action buttons component for news management
 */
export function NewsActionButtons({
  totalNews,
  onRefreshAction,
  onResetAction,
  isResetting,
  variant = "desktop",
}: NewsActionButtonsProps) {
  const [isResetDialogOpen, setIsResetDialogOpen] = useState(false);

  // Sync hook
  const { isProcessing, executeSync } = useSync({
    onSuccess: (results) => {
      console.log("Sync completed:", results);
      // Optionally refresh the news data after successful sync
      onRefreshAction();
    },
    onError: (error) => {
      console.error("Sync failed:", error);
    },
  });

  /**
   * Handles the reset action with dialog management
   */
  const handleReset = async () => {
    try {
      await onResetAction();
      setIsResetDialogOpen(false);
    } catch (error) {
      // Error handling is managed by the hook
      console.error("Reset failed:", error);
    }
  };

  const isMobile = variant === "mobile";

  return (
    <div
      className={
        isMobile
          ? "flex space-x-2 overflow-x-auto -mx-4 p-4"
          : "flex items-center space-x-3"
      }
    >
      {/* Sync Button */}
      <Button
        onClick={executeSync}
        variant="outline"
        size="lg"
        className="flex h-14 w-full items-center space-x-2 whitespace-nowrap shadow-sm hover:shadow-md transition-shadow text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300"
        disabled={isResetting || isProcessing}
      >
        {isProcessing ? (
          <>
            <Loader2 className="h-4 w-4 animate-spin" />
            {!isMobile && <span>Processando...</span>}
          </>
        ) : (
          <>
            <Play className="h-4 w-4" />
          </>
        )}
      </Button>

      {/* Refresh Button */}
      <Button
        onClick={onRefreshAction}
        variant="outline"
        size="lg"
        className="flex h-14 w-full items-center space-x-2 whitespace-nowrap shadow-sm hover:shadow-md transition-shadow"
        disabled={isResetting || isProcessing}
      >
        <RefreshCw className="h-4 w-4" />
      </Button>

      {/* Reset Button with Confirmation Dialog */}
      <AlertDialog open={isResetDialogOpen} onOpenChange={setIsResetDialogOpen}>
        <AlertDialogTrigger asChild>
          <Button
            variant="outline"
            size="lg"
            disabled={isResetting || isProcessing || totalNews === 0}
            className="flex h-14 w-full items-center space-x-2 whitespace-nowrap shadow-sm hover:shadow-md transition-shadow text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
          >
            {isResetting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                {!isMobile && <span>Limpando...</span>}
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4" />
              </>
            )}
          </Button>
        </AlertDialogTrigger>

        <AlertDialogContent className={isMobile ? "mx-4 max-w-md" : ""}>
          <AlertDialogHeader>
            <AlertDialogTitle className={isMobile ? "text-lg" : ""}>
              Confirmar Limpeza Completa
            </AlertDialogTitle>
            <AlertDialogDescription className={isMobile ? "text-sm" : ""}>
              Esta ação irá{" "}
              <strong>
                remover permanentemente todas as {totalNews} notícias
              </strong>{" "}
              do banco de dados. Esta operação não pode ser desfeita.
              <br />
              <br />
              Tem certeza que deseja continuar?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter
            className={isMobile ? "flex-col sm:flex-row gap-2" : ""}
          >
            <AlertDialogCancel className={isMobile ? "w-full sm:w-auto" : ""}>
              Cancelar
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleReset}
              disabled={isResetting || isProcessing}
              className={`bg-red-600 hover:bg-red-700 ${
                isMobile ? "w-full sm:w-auto" : ""
              }`}
            >
              {isResetting ? "Limpando..." : "Sim, limpar tudo"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

/**
 * Type exports for use in other modules
 */
export type { NewsActionButtonsProps };
