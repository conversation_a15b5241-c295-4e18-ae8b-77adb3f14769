<svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="144" height="144" rx="18" fill="url(#bgGradient)"/>
  
  <!-- News icon -->
  <rect x="36" y="36" width="72" height="50.4" rx="2.88" fill="#ffffff"/>
  <rect x="43.199999999999996" y="46.08" width="57.6" height="2.88" rx="1.44" fill="#1f2937"/>
  <rect x="43.199999999999996" y="54.72" width="43.199999999999996" height="2.88" rx="1.44" fill="#1f2937"/>
  <rect x="43.199999999999996" y="63.36" width="50.4" height="2.88" rx="1.44" fill="#1f2937"/>
  <rect x="43.199999999999996" y="72" width="36" height="2.88" rx="1.44" fill="#1f2937"/>
  
  <!-- CNEWS Text -->
  <text x="72" y="108" font-family="Arial, sans-serif" font-size="11.52" font-weight="bold" text-anchor="middle" fill="#60a5fa">CNEWS</text>
  <text x="72" y="122.39999999999999" font-family="Arial, sans-serif" font-size="6" text-anchor="middle" fill="#9ca3af">ADMIN</text>
</svg>