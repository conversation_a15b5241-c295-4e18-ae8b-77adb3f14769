"use client";

import { useState, useEffect } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { WifiOff, Wifi, RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface OfflineIndicatorProps {
  className?: string;
}

export function OfflineIndicator({ className }: OfflineIndicatorProps) {
  const [isOnline, setIsOnline] = useState(true);
  const [showOfflineMessage, setShowOfflineMessage] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);

  useEffect(() => {
    // Set initial online status
    setIsOnline(navigator.onLine);

    const handleOnline = () => {
      setIsOnline(true);
      setShowOfflineMessage(false);
      console.log('App is back online');
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowOfflineMessage(true);
      console.log('App is offline');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleRetry = async () => {
    setIsRetrying(true);
    
    try {
      // Try to fetch a small resource to test connectivity
      const response = await fetch('/manifest.json', { 
        cache: 'no-cache',
        method: 'HEAD'
      });
      
      if (response.ok) {
        setIsOnline(true);
        setShowOfflineMessage(false);
      }
    } catch (error) {
      console.log('Still offline');
    } finally {
      setIsRetrying(false);
    }
  };

  if (isOnline && !showOfflineMessage) {
    return (
      <Badge variant="outline" className={`fixed top-4 right-4 z-40 bg-green-50 text-green-700 border-green-200 ${className}`}>
        <Wifi className="h-3 w-3 mr-1" />
        Online
      </Badge>
    );
  }

  return (
    <div className={`fixed top-4 left-4 right-4 z-50 ${className}`}>
      <Alert className="bg-orange-50 border-orange-200 text-orange-800">
        <WifiOff className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <div className="flex-1">
            <strong>Você está offline.</strong> Algumas funcionalidades podem estar limitadas, mas você ainda pode visualizar notícias em cache e navegar pelo app.
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRetry}
            disabled={isRetrying}
            className="ml-4 border-orange-300 text-orange-700 hover:bg-orange-100"
          >
            {isRetrying ? (
              <RefreshCw className="h-3 w-3 animate-spin" />
            ) : (
              <RefreshCw className="h-3 w-3" />
            )}
            <span className="ml-1 hidden sm:inline">Tentar novamente</span>
          </Button>
        </AlertDescription>
      </Alert>
    </div>
  );
}
