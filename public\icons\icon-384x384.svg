<svg width="384" height="384" viewBox="0 0 384 384" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="384" height="384" rx="48" fill="url(#bgGradient)"/>
  
  <!-- News icon -->
  <rect x="96" y="96" width="192" height="134.39999999999998" rx="7.68" fill="#ffffff"/>
  <rect x="115.19999999999999" y="122.88" width="153.60000000000002" height="7.68" rx="3.84" fill="#1f2937"/>
  <rect x="115.19999999999999" y="145.92000000000002" width="115.19999999999999" height="7.68" rx="3.84" fill="#1f2937"/>
  <rect x="115.19999999999999" y="168.96" width="134.39999999999998" height="7.68" rx="3.84" fill="#1f2937"/>
  <rect x="115.19999999999999" y="192" width="96" height="7.68" rx="3.84" fill="#1f2937"/>
  
  <!-- CNEWS Text -->
  <text x="192" y="288" font-family="Arial, sans-serif" font-size="30.72" font-weight="bold" text-anchor="middle" fill="#60a5fa">CNEWS</text>
  <text x="192" y="326.4" font-family="Arial, sans-serif" font-size="15.36" text-anchor="middle" fill="#9ca3af">ADMIN</text>
</svg>