<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="512" height="512" rx="64" fill="url(#bgGradient)"/>
  
  <!-- News icon -->
  <rect x="128" y="128" width="256" height="179.2" rx="10.24" fill="#ffffff"/>
  <rect x="153.6" y="163.84" width="204.8" height="10.24" rx="5.12" fill="#1f2937"/>
  <rect x="153.6" y="194.56" width="153.6" height="10.24" rx="5.12" fill="#1f2937"/>
  <rect x="153.6" y="225.28" width="179.2" height="10.24" rx="5.12" fill="#1f2937"/>
  <rect x="153.6" y="256" width="128" height="10.24" rx="5.12" fill="#1f2937"/>
  
  <!-- CNEWS Text -->
  <text x="256" y="384" font-family="Arial, sans-serif" font-size="40.96" font-weight="bold" text-anchor="middle" fill="#60a5fa">CNEWS</text>
  <text x="256" y="435.2" font-family="Arial, sans-serif" font-size="20.48" text-anchor="middle" fill="#9ca3af">ADMIN</text>
</svg>