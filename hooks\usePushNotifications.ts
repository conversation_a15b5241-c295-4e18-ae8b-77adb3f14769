"use client";

import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

interface PushNotificationState {
  isSupported: boolean;
  permission: NotificationPermission;
  isSubscribed: boolean;
  subscription: PushSubscription | null;
  isLoading: boolean;
}

interface PushNotificationActions {
  requestPermission: () => Promise<boolean>;
  subscribe: () => Promise<boolean>;
  unsubscribe: () => Promise<boolean>;
  sendTestNotification: () => Promise<boolean>;
  checkSubscriptionStatus: () => Promise<void>;
}

// VAPID public key - In production, this should come from environment variables
const VAPID_PUBLIC_KEY = 'BEl62iUYgUivxIkv69yViEuiBIa40HI80NqIUHI-lYKCkxiWjBuL7SG7VFBdVi6F9aAd2jXwjBSQrbhHdDXh6U';

function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

export function usePushNotifications(): PushNotificationState & PushNotificationActions {
  const [state, setState] = useState<PushNotificationState>({
    isSupported: false,
    permission: 'default',
    isSubscribed: false,
    subscription: null,
    isLoading: false,
  });

  // Check if push notifications are supported
  useEffect(() => {
    const checkSupport = () => {
      const isSupported = 
        'serviceWorker' in navigator &&
        'PushManager' in window &&
        'Notification' in window;

      setState(prev => ({
        ...prev,
        isSupported,
        permission: isSupported ? Notification.permission : 'denied',
      }));
    };

    checkSupport();
  }, []);

  // Check subscription status on mount
  useEffect(() => {
    if (state.isSupported) {
      checkSubscriptionStatus();
    }
  }, [state.isSupported]);

  // Request notification permission
  const requestPermission = useCallback(async (): Promise<boolean> => {
    if (!state.isSupported) {
      toast.error('Push notifications not supported', {
        description: 'Your browser does not support push notifications',
      });
      return false;
    }

    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const permission = await Notification.requestPermission();
      
      setState(prev => ({ 
        ...prev, 
        permission,
        isLoading: false 
      }));

      if (permission === 'granted') {
        toast.success('Notifications enabled', {
          description: 'You will receive news alerts when new content is available',
        });
        return true;
      } else if (permission === 'denied') {
        toast.error('Notifications blocked', {
          description: 'Please enable notifications in your browser settings',
        });
      } else {
        toast.info('Notifications dismissed', {
          description: 'You can enable notifications later in settings',
        });
      }

      return false;
    } catch (error) {
      console.error('Failed to request notification permission:', error);
      setState(prev => ({ ...prev, isLoading: false }));
      
      toast.error('Permission request failed', {
        description: 'Failed to request notification permission',
      });
      
      return false;
    }
  }, [state.isSupported]);

  // Subscribe to push notifications
  const subscribe = useCallback(async (): Promise<boolean> => {
    if (!state.isSupported || state.permission !== 'granted') {
      const permissionGranted = await requestPermission();
      if (!permissionGranted) {
        return false;
      }
    }

    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const registration = await navigator.serviceWorker.ready;
      
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(VAPID_PUBLIC_KEY),
      });

      // Send subscription to server
      const response = await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscription: subscription.toJSON(),
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save subscription on server');
      }

      setState(prev => ({
        ...prev,
        isSubscribed: true,
        subscription,
        isLoading: false,
      }));

      toast.success('Push notifications enabled', {
        description: 'You will receive alerts for new news updates',
      });

      return true;
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);
      
      setState(prev => ({ ...prev, isLoading: false }));
      
      toast.error('Subscription failed', {
        description: 'Failed to enable push notifications',
      });
      
      return false;
    }
  }, [state.isSupported, state.permission, requestPermission]);

  // Unsubscribe from push notifications
  const unsubscribe = useCallback(async (): Promise<boolean> => {
    if (!state.subscription) {
      return true;
    }

    setState(prev => ({ ...prev, isLoading: true }));

    try {
      // Unsubscribe from push manager
      await state.subscription.unsubscribe();

      // Remove subscription from server
      await fetch('/api/push/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          endpoint: state.subscription.endpoint,
        }),
      });

      setState(prev => ({
        ...prev,
        isSubscribed: false,
        subscription: null,
        isLoading: false,
      }));

      toast.success('Push notifications disabled', {
        description: 'You will no longer receive push notifications',
      });

      return true;
    } catch (error) {
      console.error('Failed to unsubscribe from push notifications:', error);
      
      setState(prev => ({ ...prev, isLoading: false }));
      
      toast.error('Unsubscribe failed', {
        description: 'Failed to disable push notifications',
      });
      
      return false;
    }
  }, [state.subscription]);

  // Send test notification
  const sendTestNotification = useCallback(async (): Promise<boolean> => {
    if (!state.isSubscribed || !state.subscription) {
      toast.error('Not subscribed', {
        description: 'Please enable push notifications first',
      });
      return false;
    }

    try {
      const response = await fetch('/api/push/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          endpoint: state.subscription.endpoint,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send test notification');
      }

      toast.success('Test notification sent', {
        description: 'Check your notifications to see if it worked',
      });

      return true;
    } catch (error) {
      console.error('Failed to send test notification:', error);
      
      toast.error('Test failed', {
        description: 'Failed to send test notification',
      });
      
      return false;
    }
  }, [state.isSubscribed, state.subscription]);

  // Check subscription status
  const checkSubscriptionStatus = useCallback(async (): Promise<void> => {
    if (!state.isSupported) {
      return;
    }

    try {
      const registration = await navigator.serviceWorker.ready;
      const subscription = await registration.pushManager.getSubscription();

      setState(prev => ({
        ...prev,
        isSubscribed: !!subscription,
        subscription,
      }));
    } catch (error) {
      console.error('Failed to check subscription status:', error);
    }
  }, [state.isSupported]);

  return {
    ...state,
    requestPermission,
    subscribe,
    unsubscribe,
    sendTestNotification,
    checkSubscriptionStatus,
  };
}
