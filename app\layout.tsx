import "./globals.css";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { Toaster } from "@/components/ui/sonner";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "CNEWS | Admin",
  description:
    "Professional news synchronization and management platform for CNEWS",
  keywords: ["news", "admin", "management", "sync", "CNEWS", "journalism"],
  authors: [{ name: "CNEWS Team" }],
  creator: "CNEWS",
  publisher: "CNEWS",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "CNEWS Admin",
    startupImage: ["/icons/apple-touch-icon.svg"],
  },
  openGraph: {
    type: "website",
    siteName: "CNEWS Admin",
    title: "CNEWS Admin - News Management",
    description: "Professional news synchronization and management platform",
    locale: "pt_BR",
  },
  twitter: {
    card: "summary",
    title: "CNEWS Admin",
    description: "Professional news synchronization and management platform",
  },
  viewport: {
    width: "device-width",
    initialScale: 1,
    maximumScale: 1,
    userScalable: false,
  },
  themeColor: "#1f2937",
  icons: {
    icon: [
      {
        url: "/icons/favicon-16x16.svg",
        sizes: "16x16",
        type: "image/svg+xml",
      },
      {
        url: "/icons/favicon-32x32.svg",
        sizes: "32x32",
        type: "image/svg+xml",
      },
    ],
    apple: [
      {
        url: "/icons/apple-touch-icon.svg",
        sizes: "180x180",
        type: "image/svg+xml",
      },
    ],
    other: [
      {
        rel: "mask-icon",
        url: "/icons/icon-192x192.svg",
        color: "#1f2937",
      },
    ],
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="pt-BR">
      <head>
        {/* PWA Meta Tags */}
        <meta name="application-name" content="CNEWS Admin" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="CNEWS Admin" />
        <meta
          name="description"
          content="Professional news synchronization and management platform for CNEWS"
        />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-config" content="/icons/browserconfig.xml" />
        <meta name="msapplication-TileColor" content="#1f2937" />
        <meta name="msapplication-tap-highlight" content="no" />
        <meta name="theme-color" content="#1f2937" />

        {/* Preload critical resources */}
        <link
          rel="preload"
          href="/icons/icon-192x192.svg"
          as="image"
          type="image/svg+xml"
        />
        <link
          rel="preload"
          href="/manifest.json"
          as="fetch"
          crossOrigin="anonymous"
        />
      </head>
      <body className={inter.className}>
        {children}
        <Toaster position="top-right" toastOptions={{ duration: 2000 }} />
      </body>
    </html>
  );
}
