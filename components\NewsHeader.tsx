/**
 * NewsHeader - Header component with title, stats, and action buttons
 * Displays news count, view toggle, and action buttons
 */

"use client";

import { ViewToggle } from "./view-toggle";
import { NewsActionButtons } from "./NewsActionButtons";
import { Newspaper } from "lucide-react";

/**
 * Props interface for NewsHeader component
 */
interface NewsHeaderProps {
  totalNews: number;
  filteredCount: number;
  viewMode: "grid" | "list";
  onViewChangeAction: (view: "grid" | "list") => void;
  onRefreshAction: () => void;
  onResetAction: () => Promise<void>;
  isResetting: boolean;
}

/**
 * Header component for the news management interface
 */
export function NewsHeader({
  totalNews,
  filteredCount,
  viewMode,
  onViewChangeAction,
  onRefreshAction,
  onResetAction,
  isResetting,
}: NewsHeaderProps) {
  /**
   * Generates the subtitle text based on news count and filtering
   */
  const getSubtitleText = () => {
    if (totalNews === 0) {
      return "No news found";
    }

    if (filteredCount !== totalNews) {
      return `${filteredCount} of ${totalNews} news items`;
    }

    return `${totalNews} news item${totalNews > 1 ? "s" : ""} found`;
  };

  return (
    <div className="mb-4 sm:mb-6">
      {/* Mobile Layout */}
      <div className="block lg:hidden space-y-4">
        {/* Title Section */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-600 rounded-lg shadow-sm">
              <Newspaper className="h-5 w-5 text-white" />
            </div>
            <div className="min-w-0">
              <h1 className="text-lg font-semibold text-gray-900">
                Corretor News Admin
              </h1>
              <p className="text-sm text-gray-600 truncate">
                {getSubtitleText()}
              </p>
            </div>
          </div>
          {/* View Toggle - Mobile Top Right */}
          {totalNews > 0 && (
            <ViewToggle view={viewMode} onViewChange={onViewChangeAction} />
          )}
        </div>

        {/* Action Buttons - Mobile */}
        <NewsActionButtons
          totalNews={totalNews}
          onRefreshAction={onRefreshAction}
          onResetAction={onResetAction}
          isResetting={isResetting}
          variant="mobile"
        />
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:flex items-center justify-between">
        {/* Title and Stats */}
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-blue-600 rounded-lg shadow-sm">
            <Newspaper className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-semibold text-gray-900">
              Corretor News Admin
            </h1>
            <p className="text-sm text-gray-600">{getSubtitleText()}</p>
          </div>
        </div>

        {/* View Toggle and Action Buttons */}
        <div className="flex items-center space-x-3">
          {/* View Toggle - only show if we have news */}
          {totalNews > 0 && (
            <ViewToggle view={viewMode} onViewChange={onViewChangeAction} />
          )}

          {/* Action Buttons */}
          <NewsActionButtons
            totalNews={totalNews}
            onRefreshAction={onRefreshAction}
            onResetAction={onResetAction}
            isResetting={isResetting}
            variant="desktop"
          />
        </div>
      </div>
    </div>
  );
}

/**
 * Type exports for use in other modules
 */
export type { NewsHeaderProps };
