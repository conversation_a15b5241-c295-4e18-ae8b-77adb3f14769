"use client";

import { useState, useEffect, useCallback } from 'react';
import { NewsData, NewsItem } from '@/types/news';

interface OfflineDataState {
  cachedNews: NewsData;
  isOffline: boolean;
  lastSyncTime: Date | null;
  pendingOperations: PendingOperation[];
}

interface PendingOperation {
  id: string;
  type: 'mark-read' | 'send-news' | 'exclude-source' | 'sync';
  data: any;
  timestamp: Date;
  retryCount: number;
}

interface OfflineDataActions {
  cacheNewsData: (data: NewsData) => void;
  getCachedNews: () => NewsData;
  addPendingOperation: (operation: Omit<PendingOperation, 'id' | 'timestamp' | 'retryCount'>) => void;
  processPendingOperations: () => Promise<void>;
  clearCache: () => void;
  getOfflineStats: () => {
    cachedItemsCount: number;
    pendingOperationsCount: number;
    cacheSize: string;
  };
}

const STORAGE_KEYS = {
  CACHED_NEWS: 'cnews-cached-news',
  LAST_SYNC: 'cnews-last-sync',
  PENDING_OPERATIONS: 'cnews-pending-operations',
} as const;

export function useOfflineData(): OfflineDataState & OfflineDataActions {
  const [state, setState] = useState<OfflineDataState>({
    cachedNews: {},
    isOffline: false,
    lastSyncTime: null,
    pendingOperations: [],
  });

  // Initialize offline data from localStorage
  useEffect(() => {
    const initializeOfflineData = () => {
      try {
        // Load cached news
        const cachedNewsStr = localStorage.getItem(STORAGE_KEYS.CACHED_NEWS);
        const cachedNews = cachedNewsStr ? JSON.parse(cachedNewsStr) : {};

        // Load last sync time
        const lastSyncStr = localStorage.getItem(STORAGE_KEYS.LAST_SYNC);
        const lastSyncTime = lastSyncStr ? new Date(lastSyncStr) : null;

        // Load pending operations
        const pendingOpsStr = localStorage.getItem(STORAGE_KEYS.PENDING_OPERATIONS);
        const pendingOperations = pendingOpsStr ? JSON.parse(pendingOpsStr) : [];

        setState(prev => ({
          ...prev,
          cachedNews,
          lastSyncTime,
          pendingOperations: pendingOperations.map((op: any) => ({
            ...op,
            timestamp: new Date(op.timestamp),
          })),
          isOffline: !navigator.onLine,
        }));
      } catch (error) {
        console.error('Failed to initialize offline data:', error);
      }
    };

    initializeOfflineData();
  }, []);

  // Listen for online/offline events
  useEffect(() => {
    const handleOnline = () => {
      setState(prev => ({ ...prev, isOffline: false }));
      // Process pending operations when back online
      processPendingOperations();
    };

    const handleOffline = () => {
      setState(prev => ({ ...prev, isOffline: true }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Cache news data
  const cacheNewsData = useCallback((data: NewsData) => {
    try {
      localStorage.setItem(STORAGE_KEYS.CACHED_NEWS, JSON.stringify(data));
      localStorage.setItem(STORAGE_KEYS.LAST_SYNC, new Date().toISOString());
      
      setState(prev => ({
        ...prev,
        cachedNews: data,
        lastSyncTime: new Date(),
      }));
    } catch (error) {
      console.error('Failed to cache news data:', error);
    }
  }, []);

  // Get cached news
  const getCachedNews = useCallback((): NewsData => {
    return state.cachedNews;
  }, [state.cachedNews]);

  // Add pending operation
  const addPendingOperation = useCallback((operation: Omit<PendingOperation, 'id' | 'timestamp' | 'retryCount'>) => {
    const newOperation: PendingOperation = {
      ...operation,
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      retryCount: 0,
    };

    setState(prev => {
      const updatedOperations = [...prev.pendingOperations, newOperation];
      
      try {
        localStorage.setItem(STORAGE_KEYS.PENDING_OPERATIONS, JSON.stringify(updatedOperations));
      } catch (error) {
        console.error('Failed to save pending operation:', error);
      }

      return {
        ...prev,
        pendingOperations: updatedOperations,
      };
    });
  }, []);

  // Process pending operations
  const processPendingOperations = useCallback(async () => {
    if (state.isOffline || state.pendingOperations.length === 0) {
      return;
    }

    console.log(`Processing ${state.pendingOperations.length} pending operations`);

    const successfulOperations: string[] = [];
    const failedOperations: PendingOperation[] = [];

    for (const operation of state.pendingOperations) {
      try {
        await executeOperation(operation);
        successfulOperations.push(operation.id);
        console.log(`Successfully processed operation: ${operation.type}`);
      } catch (error) {
        console.error(`Failed to process operation ${operation.type}:`, error);
        
        // Retry logic
        if (operation.retryCount < 3) {
          failedOperations.push({
            ...operation,
            retryCount: operation.retryCount + 1,
          });
        } else {
          console.error(`Operation ${operation.type} failed after 3 retries, discarding`);
        }
      }
    }

    // Update pending operations
    setState(prev => {
      const updatedOperations = failedOperations;
      
      try {
        localStorage.setItem(STORAGE_KEYS.PENDING_OPERATIONS, JSON.stringify(updatedOperations));
      } catch (error) {
        console.error('Failed to update pending operations:', error);
      }

      return {
        ...prev,
        pendingOperations: updatedOperations,
      };
    });
  }, [state.isOffline, state.pendingOperations]);

  // Execute a single operation
  const executeOperation = async (operation: PendingOperation): Promise<void> => {
    switch (operation.type) {
      case 'mark-read':
        // Implement mark as read operation
        console.log('Executing mark-read operation:', operation.data);
        break;
      
      case 'send-news':
        // Implement send news operation
        console.log('Executing send-news operation:', operation.data);
        break;
      
      case 'exclude-source':
        // Implement exclude source operation
        console.log('Executing exclude-source operation:', operation.data);
        break;
      
      case 'sync':
        // Implement sync operation
        console.log('Executing sync operation:', operation.data);
        break;
      
      default:
        throw new Error(`Unknown operation type: ${(operation as any).type}`);
    }
  };

  // Clear cache
  const clearCache = useCallback(() => {
    try {
      localStorage.removeItem(STORAGE_KEYS.CACHED_NEWS);
      localStorage.removeItem(STORAGE_KEYS.LAST_SYNC);
      localStorage.removeItem(STORAGE_KEYS.PENDING_OPERATIONS);
      
      setState(prev => ({
        ...prev,
        cachedNews: {},
        lastSyncTime: null,
        pendingOperations: [],
      }));
    } catch (error) {
      console.error('Failed to clear cache:', error);
    }
  }, []);

  // Get offline stats
  const getOfflineStats = useCallback(() => {
    const cachedItemsCount = Object.keys(state.cachedNews).length;
    const pendingOperationsCount = state.pendingOperations.length;
    
    // Calculate approximate cache size
    const cacheData = localStorage.getItem(STORAGE_KEYS.CACHED_NEWS) || '';
    const cacheSize = `${(cacheData.length / 1024).toFixed(1)} KB`;

    return {
      cachedItemsCount,
      pendingOperationsCount,
      cacheSize,
    };
  }, [state.cachedNews, state.pendingOperations]);

  return {
    ...state,
    cacheNewsData,
    getCachedNews,
    addPendingOperation,
    processPendingOperations,
    clearCache,
    getOfflineStats,
  };
}
