"use client";

import { Card, CardContent } from "@/components/ui/card";
import { NewsData } from "@/types/news";
import { FileText, Users, Calendar, CheckCircle } from "lucide-react";
import { parseBrazilianDate } from "@/lib/utils";

interface NewsStatsProps {
  newsData: NewsData;
}

export function NewsStats({ newsData }: NewsStatsProps) {
  const newsArray = Object.values(newsData);
  const totalNews = newsArray.length;
  const selectedNews = newsArray.filter((item) => item.selected).length;
  const uniqueAuthors = new Set(newsArray.map((item) => item.newsAuthor)).size;

  // Calcular notícias por período
  const today = new Date();
  const todayNews = newsArray.filter((item) => {
    const itemDate = parseBrazilianDate(item.newsDate);
    return itemDate && itemDate.toDateString() === today.toDateString();
  }).length;

  const stats = [
    {
      title: "notícias",
      value: totalNews,
      icon: FileText,
      color: "bg-blue-500",
      bgColor: "bg-blue-50",
      textColor: "text-blue-700",
    },
    {
      title: "fontes",
      value: uniqueAuthors,
      icon: Users,
      color: "bg-green-500",
      bgColor: "bg-green-50",
      textColor: "text-green-700",
    },
    {
      title: "hoje",
      value: todayNews,
      icon: Calendar,
      color: "bg-orange-500",
      bgColor: "bg-orange-50",
      textColor: "text-orange-700",
    },
    {
      title: "arquivadas",
      value: selectedNews,
      icon: CheckCircle,
      color: "bg-purple-500",
      bgColor: "bg-purple-50",
      textColor: "text-purple-700",
    },
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
      {stats.map((stat, index) => (
        <Card
          key={index}
          className="border-0 shadow-sm hover:shadow-md transition-all duration-200 hover:scale-[1.02]"
        >
          <CardContent className="p-3">
            <div className="flex flex-col space-y-2">
              <div className="flex items-center space-x-2">
                <div className={`p-4 rounded-xl ${stat.bgColor} flex-shrink-0`}>
                  <stat.icon className={`h-6 w-6 ${stat.textColor}`} />
                </div>
              </div>
              <p className="text-4xl font-bold text-gray-900 p-4">
                {stat.value}{" "}
                <span className="text-sm font-normal text-gray-600 truncate flex-1">
                  {stat.title}
                </span>
              </p>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
