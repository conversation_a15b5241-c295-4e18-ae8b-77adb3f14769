<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="16" height="16" rx="2" fill="url(#bgGradient)"/>
  
  <!-- News icon -->
  <rect x="4" y="4" width="8" height="5.6" rx="0.32" fill="#ffffff"/>
  <rect x="4.8" y="5.12" width="6.4" height="0.32" rx="0.16" fill="#1f2937"/>
  <rect x="4.8" y="6.08" width="4.8" height="0.32" rx="0.16" fill="#1f2937"/>
  <rect x="4.8" y="7.04" width="5.6" height="0.32" rx="0.16" fill="#1f2937"/>
  <rect x="4.8" y="8" width="4" height="0.32" rx="0.16" fill="#1f2937"/>
  
  <!-- CNEWS Text -->
  <text x="8" y="12" font-family="Arial, sans-serif" font-size="8" font-weight="bold" text-anchor="middle" fill="#60a5fa">CNEWS</text>
  <text x="8" y="13.6" font-family="Arial, sans-serif" font-size="6" text-anchor="middle" fill="#9ca3af">ADMIN</text>
</svg>