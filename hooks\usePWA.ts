"use client";

import { useState, useEffect, useCallback } from 'react';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: "accepted" | "dismissed";
    platform: string;
  }>;
  prompt(): Promise<void>;
}

interface PWAState {
  isInstallable: boolean;
  isInstalled: boolean;
  isOffline: boolean;
  isStandalone: boolean;
  isIOS: boolean;
  deferredPrompt: BeforeInstallPromptEvent | null;
}

interface PWAActions {
  installApp: () => Promise<boolean>;
  dismissInstallPrompt: () => void;
  checkOnlineStatus: () => boolean;
  registerServiceWorker: () => Promise<boolean>;
}

export function usePWA(): PWAState & PWAActions {
  const [state, setState] = useState<PWAState>({
    isInstallable: false,
    isInstalled: false,
    isOffline: false,
    isStandalone: false,
    isIOS: false,
    deferredPrompt: null,
  });

  // Check if device is iOS
  const checkIfIOS = useCallback(() => {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  }, []);

  // Check if app is running in standalone mode
  const checkIfStandalone = useCallback(() => {
    const isStandaloneMode = window.matchMedia('(display-mode: standalone)').matches;
    const isIOSStandalone = (window.navigator as any).standalone === true;
    return isStandaloneMode || isIOSStandalone;
  }, []);

  // Check if app is installed
  const checkIfInstalled = useCallback(() => {
    return checkIfStandalone();
  }, [checkIfStandalone]);

  // Check online status
  const checkOnlineStatus = useCallback(() => {
    return navigator.onLine;
  }, []);

  // Install the app
  const installApp = useCallback(async (): Promise<boolean> => {
    if (!state.deferredPrompt) {
      console.log('No install prompt available');
      return false;
    }

    try {
      // Show the install prompt
      await state.deferredPrompt.prompt();

      // Wait for the user to respond to the prompt
      const { outcome } = await state.deferredPrompt.userChoice;

      if (outcome === 'accepted') {
        console.log('User accepted the install prompt');
        setState(prev => ({
          ...prev,
          isInstalled: true,
          isInstallable: false,
          deferredPrompt: null,
        }));
        return true;
      } else {
        console.log('User dismissed the install prompt');
        setState(prev => ({
          ...prev,
          deferredPrompt: null,
        }));
        return false;
      }
    } catch (error) {
      console.error('Error during app installation:', error);
      return false;
    }
  }, [state.deferredPrompt]);

  // Dismiss install prompt
  const dismissInstallPrompt = useCallback(() => {
    setState(prev => ({
      ...prev,
      isInstallable: false,
      deferredPrompt: null,
    }));
    
    // Store dismissal in session storage
    sessionStorage.setItem('pwa-install-dismissed', 'true');
  }, []);

  // Register service worker
  const registerServiceWorker = useCallback(async (): Promise<boolean> => {
    if (!('serviceWorker' in navigator)) {
      console.log('Service workers not supported');
      return false;
    }

    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
      });

      console.log('Service worker registered:', registration);

      // Listen for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              console.log('New service worker installed, app updated');
              // You could show a notification here about the update
            }
          });
        }
      });

      return true;
    } catch (error) {
      console.error('Service worker registration failed:', error);
      return false;
    }
  }, []);

  // Initialize PWA state
  useEffect(() => {
    const initializePWA = () => {
      const isIOS = checkIfIOS();
      const isStandalone = checkIfStandalone();
      const isInstalled = checkIfInstalled();
      const isOffline = !checkOnlineStatus();

      setState(prev => ({
        ...prev,
        isIOS,
        isStandalone,
        isInstalled,
        isOffline,
      }));
    };

    initializePWA();
  }, [checkIfIOS, checkIfStandalone, checkIfInstalled, checkOnlineStatus]);

  // Listen for beforeinstallprompt event
  useEffect(() => {
    const handleBeforeInstallPrompt = (e: BeforeInstallPromptEvent) => {
      // Prevent the mini-infobar from appearing on mobile
      e.preventDefault();
      
      // Don't show if already dismissed this session
      if (sessionStorage.getItem('pwa-install-dismissed')) {
        return;
      }

      // Save the event so it can be triggered later
      setState(prev => ({
        ...prev,
        isInstallable: true,
        deferredPrompt: e,
      }));
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  // Listen for app installed event
  useEffect(() => {
    const handleAppInstalled = () => {
      console.log('PWA was installed');
      setState(prev => ({
        ...prev,
        isInstalled: true,
        isInstallable: false,
        deferredPrompt: null,
      }));
    };

    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  // Listen for online/offline events
  useEffect(() => {
    const handleOnline = () => {
      setState(prev => ({ ...prev, isOffline: false }));
    };

    const handleOffline = () => {
      setState(prev => ({ ...prev, isOffline: true }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Register service worker on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      registerServiceWorker();
    }
  }, [registerServiceWorker]);

  return {
    ...state,
    installApp,
    dismissInstallPrompt,
    checkOnlineStatus,
    registerServiceWorker,
  };
}
