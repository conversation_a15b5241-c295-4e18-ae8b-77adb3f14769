<svg width="72" height="72" viewBox="0 0 72 72" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="72" height="72" rx="9" fill="url(#bgGradient)"/>
  
  <!-- News icon -->
  <rect x="18" y="18" width="36" height="25.2" rx="1.44" fill="#ffffff"/>
  <rect x="21.599999999999998" y="23.04" width="28.8" height="1.44" rx="0.72" fill="#1f2937"/>
  <rect x="21.599999999999998" y="27.36" width="21.599999999999998" height="1.44" rx="0.72" fill="#1f2937"/>
  <rect x="21.599999999999998" y="31.68" width="25.2" height="1.44" rx="0.72" fill="#1f2937"/>
  <rect x="21.599999999999998" y="36" width="18" height="1.44" rx="0.72" fill="#1f2937"/>
  
  <!-- CNEWS Text -->
  <text x="36" y="54" font-family="Arial, sans-serif" font-size="8" font-weight="bold" text-anchor="middle" fill="#60a5fa">CNEWS</text>
  <text x="36" y="61.199999999999996" font-family="Arial, sans-serif" font-size="6" text-anchor="middle" fill="#9ca3af">ADMIN</text>
</svg>