// CNEWS Admin PWA Service Worker
// This service worker provides offline functionality, background sync, and caching for the news admin app

const CACHE_NAME = 'cnews-admin-v1';
const OFFLINE_URL = '/offline.html';
const API_CACHE_NAME = 'cnews-api-cache-v1';

// URLs to cache for offline functionality
const STATIC_CACHE_URLS = [
  '/',
  '/offline.html',
  '/manifest.json',
  '/icons/icon-192x192.svg',
  '/icons/icon-512x512.svg'
];

// API endpoints with specific caching strategies
const API_CACHE_STRATEGIES = {
  // Firebase news data - cache with network first strategy
  FIREBASE_NEWS: {
    pattern: /.*firebase.*\/news\.json/,
    strategy: 'NetworkFirst',
    cacheName: 'firebase-news-cache',
    maxAge: 30 * 60, // 30 minutes
  },

  // Process sync endpoint - no caching (always fresh)
  PROCESS_SYNC: {
    pattern: /.*\/process-sync$/,
    strategy: 'NetworkOnly',
    cacheName: null,
    maxAge: 0,
  },

  // Sync endpoint - cache for short time
  SYNC: {
    pattern: /.*\/sync$/,
    strategy: 'NetworkFirst',
    cacheName: 'sync-cache',
    maxAge: 5 * 60, // 5 minutes
  },

  // Excluded sources endpoint - cache for medium time
  EXCLUDED_SOURCES: {
    pattern: /.*\/excluded-sources$/,
    strategy: 'NetworkFirst',
    cacheName: 'excluded-sources-cache',
    maxAge: 15 * 60, // 15 minutes
  },

  // General API fallback
  API_FALLBACK: {
    pattern: /^https:.*\.json$/,
    strategy: 'NetworkFirst',
    cacheName: 'api-fallback-cache',
    maxAge: 10 * 60, // 10 minutes
  }
};

// Install event - cache static resources
self.addEventListener('install', (event) => {
  console.log('[SW] Install event');

  event.waitUntil(
    (async () => {
      try {
        const cache = await caches.open(CACHE_NAME);
        console.log('[SW] Caching static resources');
        await cache.addAll(STATIC_CACHE_URLS);

        // Force the waiting service worker to become the active service worker
        await self.skipWaiting();
      } catch (error) {
        console.error('[SW] Failed to cache static resources:', error);
      }
    })()
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activate event');

  event.waitUntil(
    (async () => {
      try {
        // Clean up old caches
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames
            .filter(cacheName => cacheName !== CACHE_NAME && cacheName !== API_CACHE_NAME)
            .map(cacheName => {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            })
        );

        // Take control of all clients
        await self.clients.claim();
      } catch (error) {
        console.error('[SW] Failed to activate service worker:', error);
      }
    })()
  );
});

// Fetch event - handle network requests with caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests and chrome-extension requests
  if (request.method !== 'GET' || url.protocol === 'chrome-extension:') {
    return;
  }

  // Handle API requests
  if (isApiRequest(url)) {
    event.respondWith(handleApiRequest(request));
    return;
  }

  // Handle static resources
  if (isStaticResource(url)) {
    event.respondWith(handleStaticResource(request));
    return;
  }

  // Handle navigation requests (HTML pages)
  if (request.mode === 'navigate') {
    event.respondWith(handleNavigationRequest(request));
    return;
  }

  // Default: try network first, fallback to cache
  event.respondWith(
    fetch(request).catch(() => caches.match(request))
  );
});

// Background sync for news operations
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync event:', event.tag);

  if (event.tag === 'news-sync') {
    event.waitUntil(handleNewsSync());
  } else if (event.tag === 'process-sync') {
    event.waitUntil(handleProcessSync());
  }
});

// Push notification event
self.addEventListener('push', (event) => {
  console.log('[SW] Push event received');

  const options = {
    body: event.data ? event.data.text() : 'New news update available',
    icon: '/icons/icon-192x192.svg',
    badge: '/icons/icon-96x96.svg',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'View News',
        icon: '/icons/icon-96x96.svg'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/icons/icon-96x96.svg'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification('CNEWS Admin', options)
  );
});

// Notification click event
self.addEventListener('notificationclick', (event) => {
  console.log('[SW] Notification click received');

  event.notification.close();

  if (event.action === 'explore') {
    event.waitUntil(
      self.clients.openWindow('/')
    );
  }
});

// Helper functions
function getApiCacheStrategy(url) {
  const urlString = url.toString();

  for (const [key, strategy] of Object.entries(API_CACHE_STRATEGIES)) {
    if (strategy.pattern.test(urlString)) {
      return strategy;
    }
  }

  return null;
}

function isApiRequest(url) {
  return getApiCacheStrategy(url) !== null ||
    url.hostname.includes('firebase') ||
    url.hostname.includes('cnews-api');
}

function isStaticResource(url) {
  return url.pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/);
}

async function handleApiRequest(request) {
  const url = new URL(request.url);
  const cacheStrategy = getApiCacheStrategy(url);

  if (!cacheStrategy) {
    // Fallback to default behavior
    return handleDefaultApiRequest(request);
  }

  switch (cacheStrategy.strategy) {
    case 'NetworkOnly':
      return handleNetworkOnlyRequest(request);

    case 'NetworkFirst':
      return handleNetworkFirstRequest(request, cacheStrategy);

    case 'CacheFirst':
      return handleCacheFirstRequest(request, cacheStrategy);

    default:
      return handleDefaultApiRequest(request);
  }
}

async function handleNetworkOnlyRequest(request) {
  try {
    return await fetch(request);
  } catch (error) {
    console.log('[SW] Network-only request failed:', request.url);
    return new Response(
      JSON.stringify({
        error: 'Network Error',
        message: 'This operation requires an internet connection'
      }),
      {
        status: 503,
        statusText: 'Service Unavailable',
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

async function handleNetworkFirstRequest(request, strategy) {
  try {
    // Try network first
    const response = await fetch(request);

    // Cache successful responses if cacheName is provided
    if (response.ok && strategy.cacheName) {
      const cache = await caches.open(strategy.cacheName);
      const responseToCache = response.clone();

      // Add timestamp for cache expiration
      const headers = new Headers(responseToCache.headers);
      headers.set('sw-cached-at', Date.now().toString());

      const cachedResponse = new Response(responseToCache.body, {
        status: responseToCache.status,
        statusText: responseToCache.statusText,
        headers: headers
      });

      cache.put(request, cachedResponse);
    }

    return response;
  } catch (error) {
    console.log('[SW] Network failed, trying cache for:', request.url);

    // Fallback to cache
    if (strategy.cacheName) {
      const cache = await caches.open(strategy.cacheName);
      const cachedResponse = await cache.match(request);

      if (cachedResponse) {
        // Check if cache is still valid
        const cachedAt = cachedResponse.headers.get('sw-cached-at');
        if (cachedAt) {
          const cacheAge = (Date.now() - parseInt(cachedAt)) / 1000;
          if (cacheAge <= strategy.maxAge) {
            console.log('[SW] Serving from cache:', request.url);
            return cachedResponse;
          } else {
            console.log('[SW] Cache expired for:', request.url);
            cache.delete(request);
          }
        } else {
          // Old cache without timestamp, serve it anyway
          return cachedResponse;
        }
      }
    }

    // Return offline response
    return new Response(
      JSON.stringify({
        error: 'Offline',
        message: 'This request is not available offline'
      }),
      {
        status: 503,
        statusText: 'Service Unavailable',
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

async function handleCacheFirstRequest(request, strategy) {
  if (!strategy.cacheName) {
    return handleNetworkFirstRequest(request, strategy);
  }

  try {
    // Try cache first
    const cache = await caches.open(strategy.cacheName);
    const cachedResponse = await cache.match(request);

    if (cachedResponse) {
      // Check if cache is still valid
      const cachedAt = cachedResponse.headers.get('sw-cached-at');
      if (cachedAt) {
        const cacheAge = (Date.now() - parseInt(cachedAt)) / 1000;
        if (cacheAge <= strategy.maxAge) {
          console.log('[SW] Serving from cache (cache-first):', request.url);
          return cachedResponse;
        } else {
          console.log('[SW] Cache expired, fetching fresh:', request.url);
          cache.delete(request);
        }
      } else {
        // Old cache without timestamp, serve it anyway
        return cachedResponse;
      }
    }

    // Fallback to network
    const response = await fetch(request);

    if (response.ok) {
      const responseToCache = response.clone();
      const headers = new Headers(responseToCache.headers);
      headers.set('sw-cached-at', Date.now().toString());

      const cachedResponse = new Response(responseToCache.body, {
        status: responseToCache.status,
        statusText: responseToCache.statusText,
        headers: headers
      });

      cache.put(request, cachedResponse);
    }

    return response;
  } catch (error) {
    console.log('[SW] Cache-first request failed:', request.url);
    return new Response(
      JSON.stringify({
        error: 'Offline',
        message: 'This request is not available offline'
      }),
      {
        status: 503,
        statusText: 'Service Unavailable',
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

async function handleDefaultApiRequest(request) {
  try {
    // Try network first for API requests
    const response = await fetch(request);

    // Cache successful responses
    if (response.ok) {
      const cache = await caches.open(API_CACHE_NAME);
      cache.put(request, response.clone());
    }

    return response;
  } catch (error) {
    console.log('[SW] Network failed for API request, trying cache:', request.url);

    // Fallback to cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Return offline response for API requests
    return new Response(
      JSON.stringify({
        error: 'Offline',
        message: 'This request is not available offline'
      }),
      {
        status: 503,
        statusText: 'Service Unavailable',
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

async function handleStaticResource(request) {
  try {
    // Try cache first for static resources
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Fallback to network
    const response = await fetch(request);

    // Cache the response
    if (response.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, response.clone());
    }

    return response;
  } catch (error) {
    console.log('[SW] Failed to fetch static resource:', request.url);
    return new Response('Resource not available offline', { status: 404 });
  }
}

async function handleNavigationRequest(request) {
  try {
    // Try network first
    const response = await fetch(request);
    return response;
  } catch (error) {
    console.log('[SW] Navigation request failed, serving offline page');

    // Fallback to offline page
    const cache = await caches.open(CACHE_NAME);
    const offlineResponse = await cache.match(OFFLINE_URL);
    return offlineResponse || new Response('Offline', { status: 503 });
  }
}

async function handleNewsSync() {
  console.log('[SW] Handling news sync');

  try {
    // Get pending sync operations from IndexedDB or localStorage
    const pendingOperations = await getPendingSyncOperations();

    for (const operation of pendingOperations) {
      try {
        await executeNewsOperation(operation);
        await removePendingSyncOperation(operation.id);
      } catch (error) {
        console.error('[SW] Failed to sync operation:', operation, error);
      }
    }
  } catch (error) {
    console.error('[SW] Background sync failed:', error);
  }
}

async function handleProcessSync() {
  console.log('[SW] Handling process sync');
  // Implementation for process sync background operations
}

async function getPendingSyncOperations() {
  // This would typically read from IndexedDB
  // For now, return empty array
  return [];
}

async function executeNewsOperation(operation) {
  // Execute the pending news operation
  console.log('[SW] Executing news operation:', operation);
}

async function removePendingSyncOperation(operationId) {
  // Remove the completed operation from storage
  console.log('[SW] Removing completed operation:', operationId);
}
