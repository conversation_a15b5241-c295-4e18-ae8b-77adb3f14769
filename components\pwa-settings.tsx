"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { 
  Download, 
  Bell, 
  BellOff, 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  Trash2,
  Settings,
  Smartphone,
  Database
} from "lucide-react";
import { usePWA } from "@/hooks/usePWA";
import { usePushNotifications } from "@/hooks/usePushNotifications";
import { useBackgroundSync } from "@/hooks/useBackgroundSync";
import { useOfflineData } from "@/hooks/useOfflineData";

interface PWASettingsProps {
  className?: string;
}

export function PWASettings({ className }: PWASettingsProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const pwa = usePWA();
  const pushNotifications = usePushNotifications();
  const backgroundSync = useBackgroundSync();
  const offlineData = useOfflineData();

  const stats = offlineData.getOfflineStats();

  const handleInstallApp = async () => {
    const success = await pwa.installApp();
    if (success) {
      setIsExpanded(false);
    }
  };

  const handleToggleNotifications = async () => {
    if (pushNotifications.isSubscribed) {
      await pushNotifications.unsubscribe();
    } else {
      await pushNotifications.subscribe();
    }
  };

  const handleClearCache = () => {
    offlineData.clearCache();
    backgroundSync.clearPendingOperations();
  };

  const handleTestNotification = () => {
    pushNotifications.sendTestNotification();
  };

  if (!isExpanded) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsExpanded(true)}
        className={`fixed bottom-4 right-4 z-40 ${className}`}
      >
        <Settings className="h-4 w-4 mr-2" />
        PWA Settings
      </Button>
    );
  }

  return (
    <Card className={`fixed bottom-4 right-4 z-50 w-96 max-w-[calc(100vw-2rem)] shadow-lg ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">PWA Settings</CardTitle>
            <CardDescription>
              Progressive Web App configuration
            </CardDescription>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(false)}
            className="h-8 w-8 p-0"
          >
            ×
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Installation Status */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Smartphone className="h-4 w-4" />
              <span className="text-sm font-medium">App Installation</span>
            </div>
            <Badge variant={pwa.isInstalled ? "default" : "secondary"}>
              {pwa.isInstalled ? "Installed" : "Not Installed"}
            </Badge>
          </div>
          
          {!pwa.isInstalled && pwa.isInstallable && (
            <Button
              onClick={handleInstallApp}
              size="sm"
              className="w-full"
            >
              <Download className="h-4 w-4 mr-2" />
              Install App
            </Button>
          )}
          
          {pwa.isIOS && !pwa.isStandalone && (
            <p className="text-xs text-muted-foreground">
              On iOS: Use Share → Add to Home Screen
            </p>
          )}
        </div>

        <Separator />

        {/* Online/Offline Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {pwa.isOffline ? (
              <WifiOff className="h-4 w-4 text-orange-500" />
            ) : (
              <Wifi className="h-4 w-4 text-green-500" />
            )}
            <span className="text-sm font-medium">Connection</span>
          </div>
          <Badge variant={pwa.isOffline ? "destructive" : "default"}>
            {pwa.isOffline ? "Offline" : "Online"}
          </Badge>
        </div>

        <Separator />

        {/* Push Notifications */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {pushNotifications.isSubscribed ? (
                <Bell className="h-4 w-4 text-blue-500" />
              ) : (
                <BellOff className="h-4 w-4 text-gray-400" />
              )}
              <span className="text-sm font-medium">Push Notifications</span>
            </div>
            <Switch
              checked={pushNotifications.isSubscribed}
              onCheckedChange={handleToggleNotifications}
              disabled={pushNotifications.isLoading || !pushNotifications.isSupported}
            />
          </div>
          
          {pushNotifications.isSubscribed && (
            <Button
              onClick={handleTestNotification}
              size="sm"
              variant="outline"
              className="w-full"
            >
              Send Test Notification
            </Button>
          )}
          
          {!pushNotifications.isSupported && (
            <p className="text-xs text-muted-foreground">
              Push notifications not supported in this browser
            </p>
          )}
        </div>

        <Separator />

        {/* Background Sync */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              <span className="text-sm font-medium">Background Sync</span>
            </div>
            <Badge variant={backgroundSync.isSupported ? "default" : "secondary"}>
              {backgroundSync.isSupported ? "Supported" : "Not Supported"}
            </Badge>
          </div>
          
          {backgroundSync.getOperationCount() > 0 && (
            <div className="text-xs text-muted-foreground">
              {backgroundSync.getOperationCount()} pending operations
            </div>
          )}
        </div>

        <Separator />

        {/* Offline Data */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              <span className="text-sm font-medium">Offline Data</span>
            </div>
            <Badge variant="outline">
              {stats.cacheSize}
            </Badge>
          </div>
          
          <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
            <div>Cached items: {stats.cachedItemsCount}</div>
            <div>Pending ops: {stats.pendingOperationsCount}</div>
          </div>
          
          {(stats.cachedItemsCount > 0 || stats.pendingOperationsCount > 0) && (
            <Button
              onClick={handleClearCache}
              size="sm"
              variant="outline"
              className="w-full"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear Cache
            </Button>
          )}
        </div>

        <Separator />

        {/* PWA Features Summary */}
        <div className="space-y-1">
          <div className="text-xs font-medium text-muted-foreground">Features</div>
          <div className="grid grid-cols-2 gap-1 text-xs">
            <div className="flex items-center gap-1">
              <div className={`w-2 h-2 rounded-full ${pwa.isInstalled ? 'bg-green-500' : 'bg-gray-300'}`} />
              <span>Installable</span>
            </div>
            <div className="flex items-center gap-1">
              <div className={`w-2 h-2 rounded-full ${!pwa.isOffline ? 'bg-green-500' : 'bg-orange-500'}`} />
              <span>Offline Ready</span>
            </div>
            <div className="flex items-center gap-1">
              <div className={`w-2 h-2 rounded-full ${pushNotifications.isSupported ? 'bg-green-500' : 'bg-gray-300'}`} />
              <span>Push Notifications</span>
            </div>
            <div className="flex items-center gap-1">
              <div className={`w-2 h-2 rounded-full ${backgroundSync.isSupported ? 'bg-green-500' : 'bg-gray-300'}`} />
              <span>Background Sync</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
