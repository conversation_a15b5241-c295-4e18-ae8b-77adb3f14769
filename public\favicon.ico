<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="32" height="32" rx="4" fill="url(#bgGradient)"/>
  
  <!-- News icon -->
  <rect x="8" y="8" width="16" height="11.2" rx="0.64" fill="#ffffff"/>
  <rect x="9.6" y="10.24" width="12.8" height="0.64" rx="0.32" fill="#1f2937"/>
  <rect x="9.6" y="12.16" width="9.6" height="0.64" rx="0.32" fill="#1f2937"/>
  <rect x="9.6" y="14.08" width="11.2" height="0.64" rx="0.32" fill="#1f2937"/>
  <rect x="9.6" y="16" width="8" height="0.64" rx="0.32" fill="#1f2937"/>
  
  <!-- CNEWS Text -->
  <text x="16" y="24" font-family="Arial, sans-serif" font-size="8" font-weight="bold" text-anchor="middle" fill="#60a5fa">CNEWS</text>
  <text x="16" y="27.2" font-family="Arial, sans-serif" font-size="6" text-anchor="middle" fill="#9ca3af">ADMIN</text>
</svg>