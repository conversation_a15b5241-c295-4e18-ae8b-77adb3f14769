<svg width="180" height="180" viewBox="0 0 180 180" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1f2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="180" height="180" rx="22.5" fill="url(#bgGradient)"/>
  
  <!-- News icon -->
  <rect x="45" y="45" width="90" height="62.99999999999999" rx="3.6" fill="#ffffff"/>
  <rect x="54" y="57.6" width="72" height="3.6" rx="1.8" fill="#1f2937"/>
  <rect x="54" y="68.4" width="54" height="3.6" rx="1.8" fill="#1f2937"/>
  <rect x="54" y="79.2" width="62.99999999999999" height="3.6" rx="1.8" fill="#1f2937"/>
  <rect x="54" y="90" width="45" height="3.6" rx="1.8" fill="#1f2937"/>
  
  <!-- CNEWS Text -->
  <text x="90" y="135" font-family="Arial, sans-serif" font-size="14.4" font-weight="bold" text-anchor="middle" fill="#60a5fa">CNEWS</text>
  <text x="90" y="153" font-family="Arial, sans-serif" font-size="7.2" text-anchor="middle" fill="#9ca3af">ADMIN</text>
</svg>