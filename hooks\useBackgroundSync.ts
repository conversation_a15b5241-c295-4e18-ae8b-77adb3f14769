"use client";

import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

interface BackgroundSyncOperation {
  id: string;
  type: 'news-sync' | 'process-sync' | 'mark-read' | 'exclude-source';
  data: any;
  timestamp: Date;
  retryCount: number;
  maxRetries: number;
}

interface BackgroundSyncState {
  isSupported: boolean;
  isRegistered: boolean;
  pendingOperations: BackgroundSyncOperation[];
  isProcessing: boolean;
}

interface BackgroundSyncActions {
  registerBackgroundSync: () => Promise<boolean>;
  scheduleSync: (type: BackgroundSyncOperation['type'], data: any) => Promise<boolean>;
  processPendingOperations: () => Promise<void>;
  clearPendingOperations: () => void;
  getOperationCount: () => number;
}

const STORAGE_KEY = 'cnews-background-sync-operations';

export function useBackgroundSync(): BackgroundSyncState & BackgroundSyncActions {
  const [state, setState] = useState<BackgroundSyncState>({
    isSupported: false,
    isRegistered: false,
    pendingOperations: [],
    isProcessing: false,
  });

  // Check if background sync is supported
  useEffect(() => {
    const checkSupport = async () => {
      if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
        setState(prev => ({ ...prev, isSupported: true }));
        
        try {
          const registration = await navigator.serviceWorker.ready;
          setState(prev => ({ ...prev, isRegistered: true }));
        } catch (error) {
          console.error('Service worker not ready:', error);
        }
      } else {
        console.log('Background sync not supported');
      }
    };

    checkSupport();
  }, []);

  // Load pending operations from storage
  useEffect(() => {
    const loadPendingOperations = () => {
      try {
        const stored = localStorage.getItem(STORAGE_KEY);
        if (stored) {
          const operations = JSON.parse(stored).map((op: any) => ({
            ...op,
            timestamp: new Date(op.timestamp),
          }));
          setState(prev => ({ ...prev, pendingOperations: operations }));
        }
      } catch (error) {
        console.error('Failed to load pending operations:', error);
      }
    };

    loadPendingOperations();
  }, []);

  // Save pending operations to storage
  const savePendingOperations = useCallback((operations: BackgroundSyncOperation[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(operations));
    } catch (error) {
      console.error('Failed to save pending operations:', error);
    }
  }, []);

  // Register background sync
  const registerBackgroundSync = useCallback(async (): Promise<boolean> => {
    if (!state.isSupported) {
      console.log('Background sync not supported');
      return false;
    }

    try {
      const registration = await navigator.serviceWorker.ready;
      
      // Register different sync tags
      await registration.sync.register('news-sync');
      await registration.sync.register('process-sync');
      
      console.log('Background sync registered');
      return true;
    } catch (error) {
      console.error('Failed to register background sync:', error);
      return false;
    }
  }, [state.isSupported]);

  // Schedule a sync operation
  const scheduleSync = useCallback(async (
    type: BackgroundSyncOperation['type'], 
    data: any
  ): Promise<boolean> => {
    const operation: BackgroundSyncOperation = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      timestamp: new Date(),
      retryCount: 0,
      maxRetries: type === 'process-sync' ? 1 : 3, // Process sync should not retry much
    };

    // Add to pending operations
    setState(prev => {
      const updatedOperations = [...prev.pendingOperations, operation];
      savePendingOperations(updatedOperations);
      return { ...prev, pendingOperations: updatedOperations };
    });

    // If online, try to process immediately
    if (navigator.onLine) {
      try {
        await executeOperation(operation);
        
        // Remove from pending operations if successful
        setState(prev => {
          const updatedOperations = prev.pendingOperations.filter(op => op.id !== operation.id);
          savePendingOperations(updatedOperations);
          return { ...prev, pendingOperations: updatedOperations };
        });
        
        return true;
      } catch (error) {
        console.log('Immediate execution failed, will retry when online:', error);
      }
    }

    // Register background sync if supported
    if (state.isSupported && state.isRegistered) {
      try {
        const registration = await navigator.serviceWorker.ready;
        await registration.sync.register(type);
        console.log(`Background sync scheduled for: ${type}`);
      } catch (error) {
        console.error('Failed to schedule background sync:', error);
      }
    }

    return false;
  }, [state.isSupported, state.isRegistered, savePendingOperations]);

  // Process pending operations
  const processPendingOperations = useCallback(async (): Promise<void> => {
    if (state.isProcessing || state.pendingOperations.length === 0) {
      return;
    }

    setState(prev => ({ ...prev, isProcessing: true }));

    const successfulOperations: string[] = [];
    const failedOperations: BackgroundSyncOperation[] = [];

    for (const operation of state.pendingOperations) {
      try {
        await executeOperation(operation);
        successfulOperations.push(operation.id);
        console.log(`Successfully processed background sync operation: ${operation.type}`);
        
        // Show success toast for important operations
        if (operation.type === 'process-sync') {
          toast.success('News processed successfully', {
            description: 'Your news was processed in the background',
          });
        }
      } catch (error) {
        console.error(`Failed to process operation ${operation.type}:`, error);
        
        // Retry logic
        if (operation.retryCount < operation.maxRetries) {
          failedOperations.push({
            ...operation,
            retryCount: operation.retryCount + 1,
          });
        } else {
          console.error(`Operation ${operation.type} failed after ${operation.maxRetries} retries, discarding`);
          
          // Show error toast for failed operations
          toast.error('Background operation failed', {
            description: `Failed to process ${operation.type} after multiple attempts`,
          });
        }
      }
    }

    // Update pending operations
    setState(prev => {
      const updatedOperations = failedOperations;
      savePendingOperations(updatedOperations);
      return { 
        ...prev, 
        pendingOperations: updatedOperations,
        isProcessing: false 
      };
    });

    if (successfulOperations.length > 0) {
      console.log(`Processed ${successfulOperations.length} background sync operations`);
    }
  }, [state.isProcessing, state.pendingOperations, savePendingOperations]);

  // Execute a single operation
  const executeOperation = async (operation: BackgroundSyncOperation): Promise<void> => {
    switch (operation.type) {
      case 'news-sync':
        await executeNewsSync(operation.data);
        break;
      
      case 'process-sync':
        await executeProcessSync(operation.data);
        break;
      
      case 'mark-read':
        await executeMarkRead(operation.data);
        break;
      
      case 'exclude-source':
        await executeExcludeSource(operation.data);
        break;
      
      default:
        throw new Error(`Unknown operation type: ${operation.type}`);
    }
  };

  // Execute news sync operation
  const executeNewsSync = async (data: any): Promise<void> => {
    // Implementation would call the actual sync API
    console.log('Executing news sync:', data);
    
    // Simulate API call
    const response = await fetch('/api/sync', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`News sync failed: ${response.statusText}`);
    }
  };

  // Execute process sync operation
  const executeProcessSync = async (data: any): Promise<void> => {
    console.log('Executing process sync:', data);
    
    // Implementation would call the actual process-sync API
    const response = await fetch('/api/process-sync', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`Process sync failed: ${response.statusText}`);
    }
  };

  // Execute mark as read operation
  const executeMarkRead = async (data: any): Promise<void> => {
    console.log('Executing mark as read:', data);
    // Implementation would update the news item status
  };

  // Execute exclude source operation
  const executeExcludeSource = async (data: any): Promise<void> => {
    console.log('Executing exclude source:', data);
    
    const response = await fetch('/api/excluded-sources', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`Exclude source failed: ${response.statusText}`);
    }
  };

  // Clear pending operations
  const clearPendingOperations = useCallback(() => {
    setState(prev => ({ ...prev, pendingOperations: [] }));
    localStorage.removeItem(STORAGE_KEY);
  }, []);

  // Get operation count
  const getOperationCount = useCallback(() => {
    return state.pendingOperations.length;
  }, [state.pendingOperations.length]);

  // Listen for online events to process pending operations
  useEffect(() => {
    const handleOnline = () => {
      console.log('Back online, processing pending operations');
      processPendingOperations();
    };

    window.addEventListener('online', handleOnline);
    return () => window.removeEventListener('online', handleOnline);
  }, [processPendingOperations]);

  return {
    ...state,
    registerBackgroundSync,
    scheduleSync,
    processPendingOperations,
    clearPendingOperations,
    getOperationCount,
  };
}
